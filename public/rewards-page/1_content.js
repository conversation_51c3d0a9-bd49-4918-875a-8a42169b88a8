// Rewards Page JavaScript Logic
window.$REWARDS.onLifecycle({
  selector: `.rewards.content`,
  page: /^\/\w+\/pages\/rewards/,
  onMount: (_, __, kill) => {
    console.log('Rewards page mounted')

    // Base URL constant for easy future changes
    const API_URL = 'https://gateway.makroz.org'
    const BASE_URL = 'https://cdn.makroz.org/makrobet/rewards'

    // API function to fetch site claimable bonuses
    async function fetchSiteClaimableBonuses() {
      try {
        console.log('Fetching site claimable bonuses...')

        // Get auth token from localStorage
        const authToken = localStorage.getItem('s7oryO9STV')

        if (!authToken) {
          console.warn('No auth token found, user may not be logged in')
          return null
        }

        const response = await fetch(`${window.location.origin}/api/pronet/v1/site-claimable-bonuses`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${JSON.parse(authToken)}`,
            'Referer': window.location.href
          },
          credentials: 'include'
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        console.log('Site claimable bonuses fetched:', data)

        if (data.success && data.data && Array.isArray(data.data)) {
          // Filter only active bonuses
          return data.data.filter(bonus => bonus.isActive === true)
        } else {
          console.warn('Invalid response format or no bonuses found')
          return []
        }
      } catch (error) {
        console.error('Error fetching site claimable bonuses:', error)
        return null
      }
    }

    // Function to categorize bonuses by slotName
    function categorizeBonuses(bonuses) {
      if (!bonuses || !Array.isArray(bonuses)) {
        return {
          timeBonuses: [],
          happyBonuses: [],
          welcomeBonuses: []
        }
      }

      const timeBonuses = []
      const happyBonuses = []
      const welcomeBonuses = []

      bonuses.forEach(bonus => {
        if (!bonus.slotName) return

        const slotName = bonus.slotName.toLowerCase()

        if (slotName.startsWith('time_')) {
          timeBonuses.push(bonus)
        } else if (slotName.startsWith('happy_')) {
          happyBonuses.push(bonus)
        } else if (slotName === 'welcome') {
          welcomeBonuses.push(bonus)
        }
      })

      console.log('Categorized bonuses:', {
        timeBonuses: timeBonuses.length,
        happyBonuses: happyBonuses.length,
        welcomeBonuses: welcomeBonuses.length
      })

      return {
        timeBonuses,
        happyBonuses,
        welcomeBonuses
      }
    }

    // Function to get bonus icon based on type
    function getBonusIcon(bonusType) {
      const iconMap = {
        'freespin': '🎰',
        'lossback': '💰',
        'cashback': '💸',
        'deposit': '💳',
        'welcome': '🎁',
        'daily': '📅',
        'weekly': '📆',
        'monthly': '🗓️',
        'special': '⭐',
        'vip': '👑',
        'tournament': '🏆',
        'reload': '🔄'
      }

      return iconMap[bonusType?.toLowerCase()] || '🎁'
    }

    // Function to create time-based bonus card (2-card layout)
    function createTimeBonusCard(bonus) {
      const bonusName = bonus.name || 'Time Bonus'
      const bonusAmount = bonus.amount || '0'
      const bonusId = bonus.id || Math.random().toString(36).substring(2, 11)

      return `
        <div class="reward-card time-bonus" data-bonus-id="${bonusId}">
          <div class="sweep-light"></div>
          <div class="reward-content">
            <div class="reward-header">
              <h3>${bonusName}</h3>
              <p class="reward-description">Claim your time-based bonus now!</p>
            </div>
            <div class="reward-icon-wrapper">
              <div class="reward-icon">
                <div class="coin-stack"></div>
              </div>
            </div>
            <div class="reward-amount">
              <span class="currency-symbol">₺</span>${bonusAmount}
            </div>
            <button class="claim-btn" data-reward-type="time" data-bonus-id="${bonusId}">
              Claim Reward
            </button>
          </div>
        </div>
      `
    }

    // Function to create happy bonus card (3-card layout)
    function createHappyBonusCard(bonus) {
      const icon = getBonusIcon(bonus.type)
      const bonusName = bonus.name || 'Happy Bonus'
      const bonusAmount = bonus.amount || '0'
      const bonusId = bonus.id || Math.random().toString(36).substring(2, 11)

      return `
        <div class="happy-hour-card" data-bonus-id="${bonusId}">
          <div class="happy-hour-time">
            <span class="time-label">${bonusName}</span>
            <span class="time-range">Available Now</span>
          </div>
          <div class="happy-hour-bonus">
            <div class="bonus-icon">${icon}</div>
            <div class="bonus-details">
              <div class="bonus-title">${bonusName}</div>
              <div class="bonus-amount">
                <span class="currency-symbol">₺</span>${bonusAmount}
              </div>
            </div>
          </div>
          <button class="claim-btn happy-hour-btn" data-happy-hour="api" data-bonus-id="${bonusId}">
            Claim Bonus
          </button>
        </div>
      `
    }

    // Function to create welcome bonus banner (full-width)
    function createWelcomeBonusBanner(bonus) {
      const bonusName = bonus.name || 'Welcome Bonus'
      const bonusAmount = bonus.amount || '250'
      const bonusId = bonus.id || Math.random().toString(36).substring(2, 11)

      return `
        <div class="welcome-bonus-banner" data-bonus-id="${bonusId}">
          <h2>${bonusName}</h2>
          <div class="amount">${bonusAmount} TL</div>
          <p style="font-size: 16px; color: rgba(255, 255, 255, 0.8); margin-bottom: 24px; position: relative; z-index: 1;">
            New players get an instant bonus! Start your winning journey today.
          </p>
          <button class="welcome-bonus-btn" id="claim-welcome-bonus" data-bonus-id="${bonusId}">
            Claim Your Bonus
          </button>
        </div>
      `
    }

    // Function to render bonuses to the page
    async function renderBonuses() {
      console.log('Starting to render bonuses...')

      // Use retry mechanism for better reliability
      await retryRenderBonuses()
    }

    // Function to show fallback content when API fails
    function showFallbackContent() {
      console.log('Showing fallback content...')

      // Show fallback time bonuses
      const timeBonusesGrid = document.getElementById('timeBonusesGrid')
      if (timeBonusesGrid) {
        timeBonusesGrid.innerHTML = `
          <div class="reward-card time-bonus fallback">
            <div class="sweep-light"></div>
            <div class="reward-content">
              <div class="reward-header">
                <h3>Daily Bonus</h3>
                <p class="reward-description">Claim your daily bonus!</p>
              </div>
              <div class="reward-icon-wrapper">
                <div class="reward-icon">
                  <div class="coin-stack"></div>
                </div>
              </div>
              <div class="reward-amount">
                <span class="currency-symbol">₺</span>50.00
              </div>
              <button class="claim-btn" data-reward-type="fallback">
                Claim Reward
              </button>
            </div>
          </div>
          <div class="reward-card time-bonus fallback">
            <div class="sweep-light"></div>
            <div class="reward-content">
              <div class="reward-header">
                <h3>Weekly Bonus</h3>
                <p class="reward-description">Weekly bonus for loyal players!</p>
              </div>
              <div class="reward-icon-wrapper">
                <div class="reward-icon">
                  <div class="coin-stack"></div>
                </div>
              </div>
              <div class="reward-amount">
                <span class="currency-symbol">₺</span>150.00
              </div>
              <button class="claim-btn" data-reward-type="fallback">
                Claim Reward
              </button>
            </div>
          </div>
        `
        timeBonusesGrid.classList.add('two-card-layout')
      }

      // Show fallback happy bonuses
      const happyBonusesGrid = document.getElementById('happyBonusesGrid')
      if (happyBonusesGrid) {
        happyBonusesGrid.innerHTML = `
          <div class="happy-hour-card fallback">
            <div class="happy-hour-time">
              <span class="time-label">Morning Boost</span>
              <span class="time-range">09:00 - 12:00</span>
            </div>
            <div class="happy-hour-bonus">
              <div class="bonus-icon">☀️</div>
              <div class="bonus-details">
                <div class="bonus-title">Early Bird Bonus</div>
                <div class="bonus-amount">
                  <span class="currency-symbol">₺</span>25.00
                </div>
              </div>
            </div>
            <button class="claim-btn happy-hour-btn" data-happy-hour="fallback">
              Claim Bonus
            </button>
          </div>
          <div class="happy-hour-card fallback">
            <div class="happy-hour-time">
              <span class="time-label">Evening Rush</span>
              <span class="time-range">18:00 - 21:00</span>
            </div>
            <div class="happy-hour-bonus">
              <div class="bonus-icon">🌆</div>
              <div class="bonus-details">
                <div class="bonus-title">Prime Time Bonus</div>
                <div class="bonus-amount">
                  <span class="currency-symbol">₺</span>50.00
                </div>
              </div>
            </div>
            <button class="claim-btn happy-hour-btn" data-happy-hour="fallback">
              Claim Bonus
            </button>
          </div>
          <div class="happy-hour-card fallback">
            <div class="happy-hour-time">
              <span class="time-label">Weekend Special</span>
              <span class="time-range">Sat-Sun All Day</span>
            </div>
            <div class="happy-hour-bonus">
              <div class="bonus-icon">🎉</div>
              <div class="bonus-details">
                <div class="bonus-title">Weekend Warrior</div>
                <div class="bonus-amount">
                  <span class="currency-symbol">₺</span>75.00
                </div>
              </div>
            </div>
            <button class="claim-btn happy-hour-btn" data-happy-hour="fallback">
              Claim Bonus
            </button>
          </div>
        `
        happyBonusesGrid.classList.add('three-card-layout')
      }

      // Show fallback welcome bonus
      const welcomeBonusSection = document.getElementById('welcomeBonusSection')
      if (welcomeBonusSection) {
        welcomeBonusSection.innerHTML = `
          <div class="welcome-bonus-banner fallback">
            <h2>Welcome Bonus</h2>
            <div class="amount">250 TL</div>
            <p style="font-size: 16px; color: rgba(255, 255, 255, 0.8); margin-bottom: 24px; position: relative; z-index: 1;">
              New players get an instant bonus! Start your winning journey today.
            </p>
            <button class="welcome-bonus-btn" id="claim-trial-bonus">
              Claim Your Bonus
            </button>
          </div>
        `
      }

      // Re-initialize event listeners for fallback content
      setTimeout(() => {
        initializeEventListeners()
      }, 100)
    }

    // Function to show empty state
    function showEmptyState() {
      const sections = ['timeBonusesGrid', 'happyBonusesGrid', 'welcomeBonusSection']
      sections.forEach(sectionId => {
        const section = document.getElementById(sectionId)
        if (section) {
          section.innerHTML = '<div class="no-bonuses">No bonuses available at the moment</div>'
        }
      })
    }

    // Function to retry loading bonuses with exponential backoff
    async function retryRenderBonuses(maxRetries = 3, delay = 1000) {
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`Attempting to load bonuses (attempt ${attempt}/${maxRetries})...`)

          const bonuses = await fetchSiteClaimableBonuses()

          if (bonuses !== null) {
            // Success - process the bonuses
            if (bonuses.length === 0) {
              showEmptyState()
            } else {
              const { timeBonuses, happyBonuses, welcomeBonuses } = categorizeBonuses(bonuses)

              // Render bonuses
              const timeBonusesGrid = document.getElementById('timeBonusesGrid')
              if (timeBonusesGrid) {
                if (timeBonuses.length > 0) {
                  timeBonusesGrid.innerHTML = timeBonuses.map(bonus => createTimeBonusCard(bonus)).join('')
                  timeBonusesGrid.classList.add('two-card-layout')
                } else {
                  timeBonusesGrid.innerHTML = '<div class="no-bonuses">No time-based bonuses available</div>'
                }
              }

              const happyBonusesGrid = document.getElementById('happyBonusesGrid')
              if (happyBonusesGrid) {
                if (happyBonuses.length > 0) {
                  happyBonusesGrid.innerHTML = happyBonuses.map(bonus => createHappyBonusCard(bonus)).join('')
                  happyBonusesGrid.classList.add('three-card-layout')
                } else {
                  happyBonusesGrid.innerHTML = '<div class="no-bonuses">No happy bonuses available</div>'
                }
              }

              const welcomeBonusSection = document.getElementById('welcomeBonusSection')
              if (welcomeBonusSection) {
                if (welcomeBonuses.length > 0) {
                  welcomeBonusSection.innerHTML = createWelcomeBonusBanner(welcomeBonuses[0])
                } else {
                  welcomeBonusSection.innerHTML = '<div class="no-bonuses">No welcome bonus available</div>'
                }
              }

              initializeEventListeners()
              console.log('Bonuses loaded successfully on attempt', attempt)
            }
            return // Success, exit retry loop
          }

          // If this was the last attempt, show fallback
          if (attempt === maxRetries) {
            console.warn('All retry attempts failed, showing fallback content')
            showFallbackContent()
            return
          }

          // Wait before next attempt with exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)))

        } catch (error) {
          console.error(`Attempt ${attempt} failed:`, error)

          if (attempt === maxRetries) {
            console.error('All retry attempts failed, showing fallback content')
            showFallbackContent()
            return
          }

          // Wait before next attempt
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)))
        }
      }
    }

    // Replace coin stacks with PNG image
    const initializeRewardIcons = () => {
      const coinStacks = document.querySelectorAll('.coin-stack')
      coinStacks.forEach(coinStack => {
        // Create image element
        const coinImage = document.createElement('img')
        coinImage.src = `${BASE_URL}/coins.png`
        coinImage.alt = 'Reward Coins'
        coinImage.className = 'coin-image'
        coinImage.style.width = '100%'
        coinImage.style.height = '100%'
        coinImage.style.objectFit = 'contain'

        // Replace coin stack with image
        coinStack.innerHTML = ''
        coinStack.appendChild(coinImage)
      })
    }

    // Initialize icons after a short delay to ensure DOM is ready
    setTimeout(initializeRewardIcons, 100)

    // Initialize VIP container drag functionality
    const initVipDrag = () => {
      const container = document.getElementById('vipContainer')
      if (!container) return

      let isDown = false
      let startX

      container.addEventListener('mousedown', e => {
        isDown = true
        container.classList.add('dragging')
        startX = e.pageX
      })

      container.addEventListener('mouseleave', () => {
        isDown = false
        container.classList.remove('dragging')
      })

      container.addEventListener('mouseup', e => {
        if (!isDown) return
        isDown = false
        container.classList.remove('dragging')

        // Calculate drag distance and determine if we should change slides
        const endX = e.pageX
        const dragDistance = startX - endX
        const threshold = 100 // Minimum drag distance to trigger slide change

        if (Math.abs(dragDistance) > threshold) {
          const maxIndex = Math.max(0, totalCards - cardsPerView)
          if (dragDistance > 0 && currentSlideIndex < maxIndex) {
            // Dragged left - go to next slide
            currentSlideIndex++
          } else if (dragDistance < 0 && currentSlideIndex > 0) {
            // Dragged right - go to previous slide
            currentSlideIndex--
          }
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      container.addEventListener('mousemove', e => {
        if (!isDown) return
        e.preventDefault()
      })
    }

    // Initialize drag functionality after a short delay
    setTimeout(initVipDrag, 100)

    // Trial bonus function
    const claimTrialBonus = buttonElement => {
      console.log('Claiming trial bonus...')

      // Find the button element if not passed directly
      if (!buttonElement) {
        buttonElement = document.querySelector('.welcome-bonus-btn')
      }

      // Prevent multiple claims
      if (buttonElement.disabled || buttonElement.classList.contains('claimed')) return

      // Simple button state change only
      buttonElement.textContent = 'Claimed'
      buttonElement.disabled = true
      buttonElement.classList.add('claimed')

      // Apply persistent claimed state to banner without animations
      const banner = buttonElement.closest('.welcome-bonus-banner')
      if (banner) {
        banner.classList.add('bonus-claimed-state')
      }

      console.log('Welcome bonus claimed successfully!')
    }

    const claimReward = (type, buttonElement) => {
      console.log(`Claiming ${type} reward...`)

      // Find the button element if not passed directly
      if (!buttonElement) {
        buttonElement = document.querySelector(`[onclick*="${type}"]`)
      }

      // Prevent multiple claims
      if (buttonElement.disabled) return

      // Simple button state change only
      buttonElement.textContent = 'Claimed'
      buttonElement.disabled = true
      buttonElement.classList.add('claimed')

      console.log(`${type.charAt(0).toUpperCase() + type.slice(1)} reward claimed successfully!`)
    }

    document.querySelector('.apply-btn').onclick = async () => {
      const promoInput = document.getElementById('promoCode')
      const messageDiv = document.getElementById('promoMessage')

      if (!promoInput || !messageDiv) return

      const code = promoInput.value.trim().toUpperCase()

      if (!code) {
        messageDiv.textContent = 'Please enter a promo code'
        messageDiv.className = 'promo-message error'
        return
      }

      // Show loading state
      messageDiv.textContent = 'Applying promo code...'
      messageDiv.className = 'promo-message'
      promoInput.disabled = true

      try {
        // Get auth token from localStorage
        const authToken = localStorage.getItem('s7oryO9STV')

        if (!authToken) {
          messageDiv.textContent = 'Please log in to apply promo codes'
          messageDiv.className = 'promo-message error'
          promoInput.disabled = false
          return
        }

        // Make API request to activate promo code
        const response = await fetch(
          `${API_URL}/api/pronet/v1/bonus-promocodes/${encodeURIComponent(code)}/activations`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${JSON.parse(authToken)}`,
              'Content-Type': 'application/json',
              Referer: window.location.href
            }
          }
        )

        const data = await response.json()

        if (data.success) {
          messageDiv.textContent = data.message || `Promo code "${code}" applied successfully!`
          messageDiv.className = 'promo-message success'
          promoInput.value = ''
        } else {
          messageDiv.textContent = data.error || 'Invalid promo code. Please try again.'
          messageDiv.className = 'promo-message error'
        }
      } catch (error) {
        console.error('Error applying promo code:', error)
        messageDiv.textContent = 'Network error. Please try again later.'
        messageDiv.className = 'promo-message error'
      } finally {
        promoInput.disabled = false
      }
    }

    // VIP Slider functionality
    let currentSlideIndex = 0
    let cardsPerView = 3
    let totalCards = 0

    // RAF throttle function for performance optimization
    function rafThrottle(fn) {
      let locked = false
      return (...args) => {
        if (locked) return
        locked = true
        requestAnimationFrame(() => {
          fn(...args)
          locked = false
        })
      }
    }

    const initializeVipSlider = () => {
      const container = document.getElementById('vipContainer')
      const grid = container?.querySelector('.vip-ranks-grid')
      const leftArrow = document.getElementById('vipNavLeft')
      const rightArrow = document.getElementById('vipNavRight')

      if (!grid || !leftArrow || !rightArrow) return

      totalCards = grid.children.length
      updateCardsPerView()
      rafThrottledUpdateSliderPosition()
      rafThrottledUpdateArrowStates()

      // Arrow click handlers
      leftArrow.addEventListener('click', () => {
        if (currentSlideIndex > 0) {
          currentSlideIndex--
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      rightArrow.addEventListener('click', () => {
        const maxIndex = Math.max(0, totalCards - cardsPerView)
        if (currentSlideIndex < maxIndex) {
          currentSlideIndex++
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      // Touch/swipe support
      let startX = 0
      let isDragging = false

      container.addEventListener('touchstart', e => {
        startX = e.touches[0].clientX
        isDragging = true
      })

      container.addEventListener('touchmove', e => {
        if (!isDragging) return
        e.preventDefault()
      })

      container.addEventListener('touchend', e => {
        if (!isDragging) return
        isDragging = false

        const endX = e.changedTouches[0].clientX
        const diff = startX - endX

        if (Math.abs(diff) > 50) {
          // Minimum swipe distance
          if (diff > 0) {
            // Swipe left - next
            rightArrow.click()
          } else {
            // Swipe right - previous
            leftArrow.click()
          }
        }
      })

      // Resize handler
      window.addEventListener('resize', () => {
        updateCardsPerView()
        currentSlideIndex = Math.min(currentSlideIndex, Math.max(0, totalCards - cardsPerView))
        rafThrottledUpdateSliderPosition()
        rafThrottledUpdateArrowStates()
      })

      // Add scroll event listener to synchronize arrow states with manual scrolling
      container.addEventListener('scroll', () => {
        // Calculate current slide index based on scroll position
        const cardWidth = 180 + 15 // Fixed card width + gap
        const scrollPosition = Math.abs(container.scrollLeft)
        const calculatedIndex = Math.round(scrollPosition / cardWidth)

        // Update current slide index if it changed
        if (calculatedIndex !== currentSlideIndex) {
          currentSlideIndex = Math.min(calculatedIndex, Math.max(0, totalCards - cardsPerView))
          rafThrottledUpdateArrowStates()
        }
      })
    }

    const updateCardsPerView = () => {
      const width = window.innerWidth
      const container = document.querySelector('.vip-ranks-container')

      if (width < 768) {
        cardsPerView = 3
        if (container) container.style.width = '570px' // Mobile: (180 × 3) + (15 × 2) = 570px
      } else if (width < 1200) {
        cardsPerView = 5
        if (container) container.style.width = '960px' // Tablet: (180 × 5) + (15 × 4) = 960px
      } else {
        cardsPerView = 7
        if (container) container.style.width = '1350px' // Desktop: (180 × 7) + (15 × 6) = 1350px
      }
    }

    const updateSliderPosition = () => {
      const grid = document.querySelector('.vip-ranks-grid')
      if (!grid) return

      // Fixed card width and gap for precise calculation
      const cardWidth = 180
      const gap = 15
      const cardWithGap = cardWidth + gap

      const translateX = -currentSlideIndex * cardWithGap
      grid.style.transform = `translateX(${translateX}px)`
    }

    const updateArrowStates = () => {
      const leftArrow = document.getElementById('vipNavLeft')
      const rightArrow = document.getElementById('vipNavRight')

      if (leftArrow) {
        leftArrow.disabled = currentSlideIndex === 0
      }

      if (rightArrow) {
        const maxIndex = Math.max(0, totalCards - cardsPerView)
        rightArrow.disabled = currentSlideIndex >= maxIndex
      }
    }

    // Create throttled versions of functions
    const rafThrottledUpdateSliderPosition = rafThrottle(updateSliderPosition)
    const rafThrottledUpdateArrowStates = rafThrottle(updateArrowStates)

    // VIP Perks data
    const vipPerksData = {
      BRONZE: {
        name: 'Bronze VIP',
        perks: ['Welcome bonus boost', 'Basic customer support', 'Monthly cashback', 'Birthday bonus']
      },
      SILVER: {
        name: 'Silver VIP',
        perks: [
          'Enhanced welcome bonus',
          'Priority customer support',
          'Weekly cashback',
          'Birthday bonus',
          'Exclusive tournaments'
        ]
      },
      GOLD: {
        name: 'Gold VIP',
        perks: [
          'Premium welcome bonus',
          'VIP customer support',
          'Daily cashback',
          'Birthday & anniversary bonus',
          'VIP tournaments',
          'Faster withdrawals'
        ]
      },
      PLATINUM: {
        name: 'Platinum VIP',
        perks: [
          'Elite welcome bonus',
          'Dedicated account manager',
          'Enhanced daily cashback',
          'Special occasion bonuses',
          'Exclusive VIP events',
          'Priority withdrawals',
          'Personal promotions'
        ]
      },
      DIAMOND: {
        name: 'Diamond VIP',
        perks: [
          'Ultimate welcome bonus',
          'Personal VIP manager',
          'Maximum cashback rates',
          'Luxury gifts & bonuses',
          'Private VIP events',
          'Instant withdrawals',
          'Custom promotions',
          'VIP travel packages'
        ]
      },
      ELITE: {
        name: 'Elite VIP',
        perks: [
          'Exclusive elite bonuses',
          'Elite concierge service',
          'Premium cashback rates',
          'Luxury lifestyle rewards',
          'Elite-only events',
          'No withdrawal limits',
          'Bespoke promotions',
          'Elite travel experiences',
          'Personal gaming advisor'
        ]
      },
      'MACRO BLACK': {
        name: 'Macro Black VIP',
        perks: [
          'Ultimate elite bonuses',
          'Black card concierge',
          'Unlimited cashback',
          'Exclusive luxury rewards',
          'Private gaming events',
          'Unlimited withdrawals',
          'Personalized everything',
          'World-class experiences',
          'Dedicated gaming team',
          'Invitation-only privileges'
        ]
      }
    }

    const viewPerks = tier => {
      console.log(`Viewing perks for ${tier}...`)
      showVipModal(tier)
    }

    // Promo code function
    const applyPromoCode = async () => {
      const promoInput = document.getElementById('promoCode')
      const messageDiv = document.getElementById('promoMessage')

      if (!promoInput || !messageDiv) return

      const code = promoInput.value.trim().toUpperCase()

      if (!code) {
        messageDiv.textContent = 'Please enter a promo code'
        messageDiv.className = 'promo-message error'
        return
      }

      // Show loading state
      messageDiv.textContent = 'Applying promo code...'
      messageDiv.className = 'promo-message'
      promoInput.disabled = true

      try {
        // Get auth token from localStorage
        const authToken = localStorage.getItem('s7oryo9stv')

        if (!authToken) {
          messageDiv.textContent = 'Please log in to apply promo codes'
          messageDiv.className = 'promo-message error'
          promoInput.disabled = false
          return
        }

        // Make API request to activate promo code
        const response = await fetch(
          `https://gateway.makroz.org/api/pronet/v1/bonus-promocodes/${encodeURIComponent(code)}/activations`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json'
            }
          }
        )

        const data = await response.json()

        if (data.success) {
          messageDiv.textContent = data.message || `Promo code "${code}" applied successfully!`
          messageDiv.className = 'promo-message success'
          promoInput.value = ''
        } else {
          messageDiv.textContent = data.error || 'Invalid promo code. Please try again.'
          messageDiv.className = 'promo-message error'
        }
      } catch (error) {
        console.error('Error applying promo code:', error)
        messageDiv.textContent = 'Network error. Please try again later.'
        messageDiv.className = 'promo-message error'
      } finally {
        promoInput.disabled = false
      }
    }

    // Initialize event listeners
    const initializeEventListeners = () => {
      // Claim reward buttons
      const claimButtons = document.querySelectorAll('.claim-btn[data-reward-type]')
      claimButtons.forEach(button => {
        button.addEventListener('click', () => {
          const rewardType = button.getAttribute('data-reward-type')
          claimReward(rewardType, button)
        })
      })

      // Trial bonus button
      const trialBonusBtn = document.getElementById('claim-trial-bonus')
      if (trialBonusBtn) {
        trialBonusBtn.addEventListener('click', () => {
          claimTrialBonus(trialBonusBtn)
        })
      }

      // VIP tier buttons
      const vipButtons = document.querySelectorAll('.view-perks-btn[data-vip-tier]')
      vipButtons.forEach(button => {
        button.addEventListener('click', () => {
          const tier = button.getAttribute('data-vip-tier')
          viewPerks(tier)
        })
      })

      // Promo code button
      const promoBtn = document.getElementById('apply-promo-btn')
      if (promoBtn) {
        promoBtn.addEventListener('click', applyPromoCode)
      }

      // Promo code input (Enter key)
      const promoInput = document.getElementById('promoCode')
      if (promoInput) {
        promoInput.addEventListener('keypress', e => {
          if (e.key === 'Enter') {
            applyPromoCode()
          }
        })
      }
    }

    // Initialize event listeners after a short delay to ensure DOM is ready
    setTimeout(initializeEventListeners, 100)

    const showVipModal = tier => {
      const modal = document.getElementById('vipModal')
      const title = document.getElementById('vipModalTitle')
      const body = document.getElementById('vipModalBody')

      if (!modal || !title || !body) return

      // Handle MakroBlack naming convention
      let tierKey = tier.toUpperCase()
      if (tierKey === 'MAKROBLACK') {
        tierKey = 'MACRO BLACK'
      }

      const tierData = vipPerksData[tierKey]
      if (!tierData) return

      // Set modal content
      title.textContent = `${tierData.name} Benefits`

      body.innerHTML = `
        <div class="vip-modal-tier-name">${tierData.name}</div>
        <ul class="vip-modal-perks-list">
          ${tierData.perks
            .map(
              perk => `
            <li class="vip-modal-perk">
              <svg class="vip-modal-perk-icon" viewBox="0 0 24 24" fill="none">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>${perk}</span>
            </li>
          `
            )
            .join('')}
        </ul>
      `

      // Show modal
      modal.classList.add('active')
      document.body.style.overflow = 'hidden'

      // Focus management for accessibility
      const closeButton = document.getElementById('vipModalClose')
      if (closeButton) {
        closeButton.focus()
      }
    }

    const hideVipModal = () => {
      const modal = document.getElementById('vipModal')
      if (modal) {
        modal.classList.remove('active')
        document.body.style.overflow = ''
      }
    }

    // Modal event listeners
    const initializeVipModal = () => {
      const modal = document.getElementById('vipModal')
      const backdrop = document.getElementById('vipModalBackdrop')
      const closeButton = document.getElementById('vipModalClose')

      if (closeButton) {
        closeButton.addEventListener('click', hideVipModal)
      }

      if (backdrop) {
        backdrop.addEventListener('click', hideVipModal)
      }

      // Escape key to close
      document.addEventListener('keydown', e => {
        if (e.key === 'Escape' && modal?.classList.contains('active')) {
          hideVipModal()
        }
      })
    }

    // Initialize everything after DOM is ready
    setTimeout(() => {
      initializeVipSlider()
      initializeVipModal()
      renderBonuses() // Load and render bonuses from API
    }, 500)

    // Cleanup function
    kill(() => {
      // Remove CSS link
      const cssLink = document.querySelector('link[href="/rewards-page/1_content.css"]')
      if (cssLink) {
        cssLink.remove()
      }
    })
  }
})
