const isMatchingPage = pattern => {
  return !pattern || pattern.test(window.location.pathname)
}

// Environment Configuration - Available globally for all header scripts
const ENVIRONMENT_HOST = 'http://127.0.0.1:3000';
// const ENVIRONMENT_HOST = 'https://gateway.makroz.org';

window.$ENVIRONMENT = {
  HOST: ENVIRONMENT_HOST,
  API_ENDPOINTS: {
    CALL_REQUESTS: `${ENVIRONMENT_HOST}/api/pronet/v1/call-requests`,
    BONUS_REQUESTS: `${ENVIRONMENT_HOST}/api/pronet/v1/bonus-requests`,
    MARKET_REQUESTS: `${ENVIRONMENT_HOST}/api/pronet/v1/market-requests`,
  },
  isProduction: () => ENVIRONMENT_HOST.includes('gateway.makroz.org'),
  isLocalDevelopment: () => ENVIRONMENT_HOST.includes('localhost') || ENVIRONMENT_HOST.includes('127.0.0.1'),
  isStaging: () => ENVIRONMENT_HOST.includes('staging'),
};

console.log('🌍 Environment Configuration Loaded:', {
  host: ENVIRONMENT_HOST,
  isProduction: window.$ENVIRONMENT.isProduction(),
  isLocalDevelopment: window.$ENVIRONMENT.isLocalDevelopment()
});

window.$HEADER = {
  getLang: () => document.documentElement.lang,
  navigate: urlOrPath => {
    let path = urlOrPath

    if (urlOrPath.__proto__ === URL.prototype) {
      path = `${urlOrPath.pathname}${urlOrPath.search}`
    } else {
      const lang = document.documentElement.lang
      path = '/' + lang + path
    }

    const ctx = document.querySelector('app-root').__ngContext__
    for (const ctxItem of ctx) {
      if (ctxItem && ctxItem.router && ctxItem.router.__proto__.constructor.toString().includes('router')) {
        ctxItem.router.navigateByUrl(path)
        break
      }
    }

    // window.location.href = `/${lang}${path}`;

    // if (path.startsWith('/' + lang)) {
    //   history.pushState({}, '', path)
    // } else {
    //   history.pushState({}, '', lang + '/' + (path.startsWith('/') ? path.substring(1) : path))
    // }

    // window.dispatchEvent(new PopStateEvent('popstate'))
  },
  onLifecycle: ({ selector, page, onMount, ...props }) => {
    const watch = props.watch ?? selector
    let onUnmount = props.onUnmount
    let target = null
    let watchTarget = null

    const observer = new MutationObserver(mutations => {
      for (const mutation of mutations) {
        if (mutation.type !== `childList`) {
          continue
        }

        for (const node of mutation.removedNodes) {
          if (!target && !watchTarget) {
            continue
          }

          if (isMatchingPage(page)) {
            if (node.nodeType !== Node.ELEMENT_NODE) {
              continue
            }

            if (node !== watchTarget && node.contains(watchTarget) === false) {
              continue
            }
          }

          onUnmount?.(target)
          target = null
          watchTarget = null
          break
        }

        if (isMatchingPage(page) === false) {
          break
        }

        for (const node of mutation.addedNodes) {
          if (target) {
            continue
          }

          if (node.nodeType !== Node.ELEMENT_NODE) {
            continue
          }

          watchTarget = document.querySelector(watch ?? selector)
          if (!watchTarget) {
            continue
          }

          if (watch === selector) {
            target = watchTarget
            onUnmount = onMount(target, watchTarget, () => observer.disconnect()) ?? props.onUnmount
            break
          } else {
            target = document.querySelector(selector)
            if (!target) continue
            onUnmount = onMount(target, watchTarget, () => observer.disconnect()) ?? props.onUnmount
            break
          }
        }
      }
    })

    if (isMatchingPage(page)) {
      watchTarget = document.querySelector(watch)
      if (watchTarget) {
        if (watch === selector) {
          target = watchTarget
          onUnmount = onMount(target, watchTarget, () => observer.disconnect()) ?? props.onUnmount
        } else {
          target = document.querySelector(selector)
          if (target) {
            onUnmount = onMount(target, watchTarget, () => observer.disconnect()) ?? props.onUnmount
          }
        }
      }
    }

    observer.observe(document.body, { childList: true, subtree: true })
  }
}